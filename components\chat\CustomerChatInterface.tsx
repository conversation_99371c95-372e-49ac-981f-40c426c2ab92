/**
 * ## Customer Chat Interface
 * Simple 1-on-1 chat interface for customers to communicate with admin support
 * Customers only see their own conversation with admin - no access to other customers' chats
 */

'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import {
  Send,
  MoreVertical,
  Paperclip,
  Smile,
  Mic,
  MessageSquare,
  Headphones,
  Clock,
  CheckCheck
} from 'lucide-react'
import { useChat } from '@/lib/hooks/useChat'
import { formatTime } from '@/lib/utils/dateUtils'

interface CustomerChatInterfaceProps {
  userId: string
  userName?: string
  userEmail?: string
}

export function CustomerChatInterface({ 
  userId, 
  userName = 'عميل',
  userEmail 
}: CustomerChatInterfaceProps) {
  const [messageInput, setMessageInput] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Chat hook - customer only sees their own conversation with admin
  const {
    messages,
    isLoadingMessages,
    sendMessage,
    markAsRead,
    typingUsers,
    error,
    isConnected
  } = useChat({
    userId,
    userType: 'customer'
  })

  /**
   * ## Auto-scroll to latest message
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  /**
   * ## Handle message sending
   */
  const handleSendMessage = async () => {
    if (!messageInput.trim()) return

    const message = messageInput.trim()
    setMessageInput('')
    
    try {
      await sendMessage(message)
      inputRef.current?.focus()
    } catch (err) {
      console.error('Error sending message:', err)
    }
  }

  /**
   * ## Handle Enter key press
   */
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  /**
   * ## Mark messages as read when component mounts
   */
  useEffect(() => {
    const unreadMessages = messages
      .filter(msg => !msg.isRead && msg.senderType === 'admin')
      .map(msg => msg.id)
    
    if (unreadMessages.length > 0) {
      markAsRead(unreadMessages)
    }
  }, [messages, markAsRead])

  return (
    <div className="flex flex-col h-[600px] md:h-[700px] bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
      
      {/* Chat Header - Support Team */}
      <div className="p-4 bg-slate-800/80 border-b border-slate-700/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Support Team Avatar */}
            <div className="relative">
              <Avatar className="h-10 w-10 border-2 border-green-400/20">
                <AvatarImage src="/support-avatar.png" alt="فريق الدعم" />
                <AvatarFallback className="bg-gradient-to-br from-green-400 to-green-600 text-white font-bold">
                  د
                </AvatarFallback>
              </Avatar>
              {isConnected && (
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-slate-800 rounded-full"></div>
              )}
            </div>

            {/* Support Info */}
            <div>
              <h3 className="font-semibold text-white">فريق الدعم الفني</h3>
              <div className="flex items-center gap-2 text-xs">
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                <span className="text-slate-400">
                  {isConnected ? 'متصل الآن' : 'غير متصل'}
                </span>
              </div>
            </div>
          </div>

          {/* Header Actions */}
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4">
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 text-red-300 p-3 rounded-xl text-sm mb-4">
            {error}
          </div>
        )}

        {isLoadingMessages ? (
          <div className="flex items-center justify-center p-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center p-12">
            <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4 shadow-lg">
              <Headphones className="h-8 w-8" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">مرحباً بك في الدعم الفني</h3>
            <p className="text-slate-400 text-sm mb-4">
              نحن هنا لمساعدتك! اكتب رسالتك وسنرد عليك في أقرب وقت ممكن
            </p>
            <div className="flex items-center justify-center gap-2 text-xs text-slate-500">
              <Clock className="h-3 w-3" />
              <span>متوسط وقت الرد: أقل من 5 دقائق</span>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.senderType === 'customer' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`
                  max-w-[80%] rounded-2xl px-4 py-3 shadow-lg
                  ${message.senderType === 'customer' 
                    ? 'bg-gradient-to-r from-green-500 to-green-600 text-white' 
                    : 'bg-slate-700/80 text-white border border-slate-600/50'
                  }
                `}>
                  <p className="text-sm leading-relaxed">{message.content}</p>
                  <div className={`
                    flex items-center gap-1 mt-2 text-xs
                    ${message.senderType === 'customer' ? 'text-green-100' : 'text-slate-400'}
                  `}>
                    <span>{formatTime(message.timestamp)}</span>
                    {message.senderType === 'customer' && (
                      <CheckCheck className={`h-3 w-3 ${message.isRead ? 'text-green-200' : 'text-green-300'}`} />
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {/* Typing Indicator */}
            {typingUsers.length > 0 && (
              <div className="flex justify-start">
                <div className="bg-slate-700/80 rounded-2xl px-4 py-3 border border-slate-600/50">
                  <div className="flex items-center gap-2">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <span className="text-xs text-slate-400">فريق الدعم يكتب...</span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        )}
      </ScrollArea>

      {/* Input Area */}
      <div className="border-t border-slate-700/50 p-4 bg-slate-800/50">
        <div className="flex items-center gap-3">
          {/* Attachment Button */}
          <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white p-2">
            <Paperclip className="h-4 w-4" />
          </Button>

          {/* Message Input */}
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="اكتب رسالتك هنا..."
              className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 rounded-xl px-4 py-3 pr-12 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/20"
              maxLength={1000}
              disabled={!isConnected}
            />
            
            {/* Emoji Button */}
            <Button 
              variant="ghost" 
              size="sm" 
              className="absolute left-2 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white p-1"
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>

          {/* Send/Voice Button */}
          {messageInput.trim() ? (
            <Button
              onClick={handleSendMessage}
              disabled={!messageInput.trim() || !isConnected}
              className="bg-green-500 hover:bg-green-600 text-white rounded-xl px-4 py-3 transition-all duration-200 hover:scale-105"
            >
              <Send className="h-4 w-4" />
            </Button>
          ) : (
            <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white p-3">
              <Mic className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Connection Status */}
        {!isConnected && (
          <div className="flex items-center justify-center gap-2 mt-2 text-xs text-red-400">
            <div className="w-2 h-2 bg-red-400 rounded-full"></div>
            <span>انقطع الاتصال - جاري إعادة المحاولة...</span>
          </div>
        )}
      </div>
    </div>
  )
}
